# Hybrid Editing Data Flow Investigation

## Overview

This document outlines the comprehensive investigation implemented to debug the data flow issue in GretahAI ScriptWeaver where hybrid-edited test case steps are not being properly used as the single source of truth in subsequent step-level stages.

## Problem Statement

**Issue**: After applying hybrid editing changes in Stage 3 ("Apply to Test Case"), all subsequent step-level stages (Stage 4, Stage 5, Stage 6, etc.) should use the hybrid-edited steps as the authoritative data source. However, the system is currently generating scripts based on the original test case steps instead of the hybrid-edited steps.

## Investigation Implementation

### 1. Enhanced Logging in `get_effective_step_table()`

**File**: `state_manager.py`
**Function**: `get_effective_step_table()`

Added comprehensive logging to track:
- Which path the function takes (hybrid editing vs original steps)
- Step counts and content for each data source
- First step details for debugging
- Clear indicators of which step table is being returned

### 2. Stage 4 Data Flow Logging

**File**: `stages/stage4.py`
**Functions**: `stage4_detect_elements()`, step selection logic

Added logging to track:
- Effective step table retrieval and sync operations
- Step table source and content analysis
- Step selection and data storage process
- Data consistency checks between original and step table entries
- Hybrid editing detection and warnings

### 3. Stage 5 Data Flow Logging

**File**: `stages/stage5.py`
**Function**: `stage5_test_data()`

Added logging to track:
- Current step data from both sources
- Hybrid editing detection in test data stage
- Data consistency validation

### 4. Stage 6 Script Generation Logging

**File**: `stages/stage6.py`
**Function**: `stage6_generate_script()`

Added comprehensive logging to track:
- Step data retrieval and analysis
- Data consistency checks before AI calls
- Critical decision points for hybrid editing
- Exact data being sent to AI for script generation
- Hybrid editing detection and warnings

### 5. Data Consistency Validation Function

**File**: `state_manager.py`
**Function**: `validate_step_data_consistency()`

Added a centralized validation function that:
- Checks if hybrid editing is active
- Compares data sources for consistency
- Identifies action mismatches
- Provides recommendations for fixes
- Logs comprehensive validation results

### 6. Debug Test Script

**File**: `debug_hybrid_data_flow.py`

Created a comprehensive test script that:
- Sets up test state with hybrid editing
- Simulates the complete workflow
- Tests data flow through all stages
- Generates detailed reports
- Identifies where data flow breaks down

## Key Investigation Points

### 1. Data Source Priority

The investigation tracks which data source is used at each stage:
- **Original Steps**: `state.selected_test_case['Steps']`
- **Step Table JSON**: `state.step_table_json` 
- **Combined Step Table**: `state.combined_step_table`
- **Effective Step Table**: `state.get_effective_step_table()`

### 2. Critical Decision Points

The logging identifies critical decision points where the wrong data source might be used:
- Stage 4: Step selection from effective step table
- Stage 4: Storage of `selected_step_table_entry` vs `selected_step`
- Stage 6: Data passed to AI for script generation

### 3. Hybrid Editing Detection

The investigation tracks hybrid editing indicators:
- Action mismatches between original and step table entries
- Manual step flags (`_is_manual`)
- AI-generated step flags (`_is_ai_generated`)
- Synthetic step creation for manually added steps

## How to Use the Investigation Tools

### 1. Run the Debug Script

```bash
cd GretahAI_ScriptWeaver
python debug_hybrid_data_flow.py
```

This will:
- Create a test scenario with hybrid editing
- Simulate the complete workflow
- Generate a timestamped log file in `debug_logs/`
- Provide a summary report

### 2. Monitor Live Application Logs

When running the actual application, the enhanced logging will automatically capture:
- All data flow operations
- Hybrid editing detection
- Data consistency issues
- Critical decision points

Look for these log patterns:
- `=== get_effective_step_table() called ===`
- `Stage 4: ✓ Found step table entry`
- `Stage 6: 🔥 CRITICAL: Hybrid editing detected`
- `🚨 CRITICAL DATA INCONSISTENCY DETECTED!`

### 3. Analyze Validation Results

The `validate_step_data_consistency()` function provides structured validation results:

```python
validation_results = state.validate_step_data_consistency("Stage Name")
if not validation_results["data_sources_consistent"]:
    # Data inconsistency detected
    for issue in validation_results["issues"]:
        print(f"Issue: {issue}")
    for rec in validation_results["recommendations"]:
        print(f"Recommendation: {rec}")
```

## Expected Investigation Outcomes

### 1. Identify Root Cause

The investigation should reveal:
- Which stage is not using `get_effective_step_table()`
- Where data synchronization breaks down
- If `selected_step_table_entry` contains correct hybrid data

### 2. Detect Data Flow Issues

Common issues to look for:
- Stage 4 not calling `get_effective_step_table()`
- `sync_step_table_with_combined()` not working properly
- Stage 6 using wrong data source for AI calls
- State management inconsistencies

### 3. Verify Fixes

After implementing fixes, the investigation tools can verify:
- Hybrid-edited steps are used consistently
- Script generation uses correct step content
- Data flow is consistent across all stages

## Next Steps

1. **Run the debug script** to establish baseline behavior
2. **Analyze the logs** to identify where data flow breaks down
3. **Test with real hybrid editing** in the application
4. **Monitor the enhanced logging** during actual usage
5. **Implement fixes** based on investigation findings
6. **Re-run validation** to confirm fixes work

## Log File Locations

- Debug script logs: `debug_logs/hybrid_data_flow_debug_YYYYMMDD_HHMMSS.log`
- Application logs: Check your application's logging configuration
- AI interaction logs: `ai_logs/` (if configured)

## Key Logging Patterns to Watch

- **Hybrid Editing Detection**: Look for action mismatches
- **Data Source Usage**: Track which step table is being used
- **Critical Warnings**: `🔥 CRITICAL` and `🚨` indicators
- **Validation Failures**: Data consistency issues
- **Stage Transitions**: Ensure data persists correctly

This investigation framework provides comprehensive visibility into the hybrid editing data flow and should help identify and resolve the root cause of the issue.
