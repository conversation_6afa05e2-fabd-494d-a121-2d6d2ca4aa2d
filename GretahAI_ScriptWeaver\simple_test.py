"""Simple test for step data storage."""

import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

try:
    print("Testing step data storage import...")
    from core.step_data_storage import StepDataStorage
    print("✅ Import successful!")
    
    # Test basic functionality
    print("Testing basic functionality...")
    storage = StepDataStorage()
    print("✅ Storage instance created!")
    
    # Test save
    test_data = [{"step_no": "1", "action": "test"}]
    result = storage.save_step_data("TEST_001", test_data)
    print(f"Save result: {result}")
    
    # Test load
    loaded = storage.load_step_data("TEST_001")
    print(f"Load result: {loaded is not None}")
    
    print("🎉 Basic test completed successfully!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
