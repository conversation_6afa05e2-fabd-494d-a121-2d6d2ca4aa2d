"""
Test script for the persistent step data storage system.

This script tests the JSON-based persistence functionality to ensure
hybrid-edited test case steps are consistently saved and loaded.
"""

import os
import sys
import json
import tempfile
import shutil
from datetime import datetime
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_step_data_storage():
    """Test the step data storage functionality."""
    print("🧪 Testing Step Data Storage System")
    print("=" * 50)
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Using temporary directory: {temp_dir}")
        
        # Import the storage module
        from core.step_data_storage import StepDataStorage
        
        # Initialize storage with test directory
        storage = StepDataStorage(temp_dir)
        
        # Test data
        test_case_id = "TC_001_Login_Test"
        test_step_data = [
            {
                "step_no": "1",
                "action": "navigate",
                "locator_strategy": "",
                "locator": "",
                "test_data_param": "https://example.com/login",
                "expected_result": "Login page loads",
                "timeout": 10,
                "_is_ai_generated": True,
                "_is_locked": True
            },
            {
                "step_no": "2",
                "action": "type",
                "locator_strategy": "id",
                "locator": "username",
                "test_data_param": "{username}",
                "expected_result": "Username entered",
                "timeout": 10,
                "_is_ai_generated": True,
                "_is_locked": True
            },
            {
                "step_no": "2.1",
                "action": "wait",
                "locator_strategy": "",
                "locator": "",
                "test_data_param": "2",
                "expected_result": "Page stabilizes",
                "timeout": 5,
                "_is_manual": True,
                "_step_id": "manual_wait_001"
            },
            {
                "step_no": "3",
                "action": "type",
                "locator_strategy": "id",
                "locator": "password",
                "test_data_param": "{password}",
                "expected_result": "Password entered",
                "timeout": 10,
                "_is_ai_generated": True,
                "_is_locked": True
            }
        ]
        
        test_metadata = {
            "source": "test_hybrid_editing",
            "ai_steps_count": 3,
            "manual_steps_count": 1,
            "test_objective": "Test login functionality with manual wait step"
        }
        
        # Test 1: Save step data
        print("\n📝 Test 1: Saving step data")
        save_success = storage.save_step_data(test_case_id, test_step_data, test_metadata)
        
        if save_success:
            print("✅ Step data saved successfully")
        else:
            print("❌ Failed to save step data")
            return False
        
        # Test 2: Load step data
        print("\n📖 Test 2: Loading step data")
        loaded_data = storage.load_step_data(test_case_id)
        
        if loaded_data:
            loaded_steps, loaded_metadata = loaded_data
            print(f"✅ Step data loaded successfully")
            print(f"   → Steps count: {len(loaded_steps)}")
            print(f"   → Metadata keys: {list(loaded_metadata.keys())}")
            
            # Verify data integrity
            if len(loaded_steps) == len(test_step_data):
                print("✅ Step count matches")
            else:
                print(f"❌ Step count mismatch: expected {len(test_step_data)}, got {len(loaded_steps)}")
                return False
                
            # Check first step
            if loaded_steps[0]["action"] == test_step_data[0]["action"]:
                print("✅ First step action matches")
            else:
                print(f"❌ First step action mismatch")
                return False
                
        else:
            print("❌ Failed to load step data")
            return False
        
        # Test 3: Save updated data (simulating hybrid editing)
        print("\n🔄 Test 3: Updating step data (hybrid editing simulation)")
        
        # Add another manual step
        updated_steps = loaded_steps.copy()
        updated_steps.append({
            "step_no": "4.1",
            "action": "verify",
            "locator_strategy": "css",
            "locator": ".success-message",
            "test_data_param": "",
            "expected_result": "Success message displayed",
            "timeout": 10,
            "_is_manual": True,
            "_step_id": "manual_verify_001"
        })
        
        updated_metadata = {
            "source": "test_hybrid_editing_update",
            "ai_steps_count": 3,
            "manual_steps_count": 2,
            "update_timestamp": datetime.now().isoformat()
        }
        
        update_success = storage.save_step_data(test_case_id, updated_steps, updated_metadata)
        
        if update_success:
            print("✅ Updated step data saved successfully")
        else:
            print("❌ Failed to save updated step data")
            return False
        
        # Test 4: Load updated data
        print("\n📖 Test 4: Loading updated step data")
        updated_loaded_data = storage.load_step_data(test_case_id)
        
        if updated_loaded_data:
            updated_loaded_steps, updated_loaded_metadata = updated_loaded_data
            print(f"✅ Updated step data loaded successfully")
            print(f"   → Steps count: {len(updated_loaded_steps)}")
            print(f"   → Manual steps count: {updated_loaded_metadata.get('manual_steps_count', 'N/A')}")
            
            if len(updated_loaded_steps) == 5:  # 3 AI + 2 manual
                print("✅ Updated step count is correct")
            else:
                print(f"❌ Updated step count incorrect: expected 5, got {len(updated_loaded_steps)}")
                return False
                
        else:
            print("❌ Failed to load updated step data")
            return False
        
        # Test 5: Test non-existent test case
        print("\n🔍 Test 5: Loading non-existent test case")
        non_existent_data = storage.load_step_data("TC_999_NonExistent")
        
        if non_existent_data is None:
            print("✅ Correctly returned None for non-existent test case")
        else:
            print("❌ Should have returned None for non-existent test case")
            return False
        
        # Test 6: Check file structure
        print("\n📁 Test 6: Verifying file structure")
        storage_path = Path(temp_dir)
        json_files = list(storage_path.glob("step_data_*.json"))
        
        print(f"   → Found {len(json_files)} JSON files")
        for file_path in json_files:
            print(f"   → {file_path.name}")
            
        if len(json_files) >= 2:  # Should have at least 2 versions
            print("✅ Multiple versions saved correctly")
        else:
            print("❌ Expected multiple file versions")
            return False
    
    print("\n🎉 All tests passed! Step data persistence system is working correctly.")
    return True


def test_state_manager_integration():
    """Test integration with StateManager."""
    print("\n🔗 Testing StateManager Integration")
    print("=" * 50)
    
    try:
        from state_manager import StateManager
        
        # Create a mock StateManager instance
        state = StateManager()
        
        # Set up test case
        state.selected_test_case = {
            "Test Case ID": "TC_002_Integration_Test",
            "Test Case Objective": "Test StateManager integration"
        }
        
        # Test step data
        test_steps = [
            {
                "step_no": "1",
                "action": "navigate",
                "expected_result": "Page loads"
            },
            {
                "step_no": "2", 
                "action": "click",
                "expected_result": "Button clicked"
            }
        ]
        
        # Test save
        print("📝 Testing StateManager save_step_data_to_json...")
        save_success = state.save_step_data_to_json(test_steps, {"source": "integration_test"})
        
        if save_success:
            print("✅ StateManager save successful")
        else:
            print("❌ StateManager save failed")
            return False
        
        # Test load
        print("📖 Testing StateManager load_step_data_from_json...")
        loaded_data = state.load_step_data_from_json()
        
        if loaded_data:
            loaded_steps, loaded_metadata = loaded_data
            print(f"✅ StateManager load successful")
            print(f"   → Steps: {len(loaded_steps)}")
            print(f"   → Source: {loaded_metadata.get('source', 'N/A')}")
        else:
            print("❌ StateManager load failed")
            return False
        
        print("✅ StateManager integration test passed!")
        return True
        
    except Exception as e:
        print(f"❌ StateManager integration test failed: {e}")
        return False


if __name__ == "__main__":
    print("🚀 Starting Step Data Persistence Tests")
    print("=" * 60)
    
    # Run tests
    storage_test_passed = test_step_data_storage()
    integration_test_passed = test_state_manager_integration()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary")
    print("=" * 60)
    print(f"Storage System Test: {'✅ PASSED' if storage_test_passed else '❌ FAILED'}")
    print(f"StateManager Integration: {'✅ PASSED' if integration_test_passed else '❌ FAILED'}")
    
    if storage_test_passed and integration_test_passed:
        print("\n🎉 All tests passed! The persistent step data storage system is ready for use.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        sys.exit(1)
