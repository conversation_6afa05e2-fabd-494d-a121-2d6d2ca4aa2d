# Persistent Step Data Storage System

## Overview

The Persistent Step Data Storage System ensures that hybrid-edited test case steps are consistently used as the single source of truth across all stages and application restarts. This system addresses data flow inconsistencies by implementing a JSON file-based persistence layer that takes priority over in-memory state.

## Key Features

### 🔄 **Persistent JSON Storage**
- **File-based persistence**: Step data is saved to JSON files with unique timestamps
- **Automatic versioning**: Multiple versions are kept for each test case (configurable limit)
- **Data integrity**: SHA256 hashing ensures data hasn't been corrupted
- **Cross-platform compatibility**: Works on Windows, macOS, and Linux

### 🎯 **Single Source of Truth**
- **Priority hierarchy**: JSON files → Combined steps → Original steps
- **Automatic synchronization**: All hybrid editing operations save to JSON
- **State consistency**: Eliminates data flow inconsistencies between stages
- **Session persistence**: Data survives application restarts

### 🔀 **Hybrid Editing Integration**
- **Initial conversion**: AI-generated steps are saved immediately after conversion
- **Manual step additions**: Combined steps are saved when "Apply to Test Case" is used
- **Real-time updates**: All step modifications trigger JSON persistence
- **Metadata tracking**: Rich metadata about editing operations and sources

## Architecture

### File Structure
```
step_data_storage/
├── step_data_TC001_20250530_113306.json
├── step_data_TC001_20250530_114522.json
├── step_data_TC002_20250530_115033.json
└── ...
```

### File Naming Convention
```
step_data_{test_case_id}_{timestamp}.json
```

### JSON File Format
```json
{
  "test_case_id": "TC_001_Login_Test",
  "timestamp": "2025-05-30T11:33:06.528424",
  "data_hash": "42ac52f29caa91eb...",
  "step_count": 4,
  "metadata": {
    "source": "hybrid_editing_apply",
    "ai_steps_count": 3,
    "manual_steps_count": 1,
    "conversion_method": "ai_generated",
    "validation_score": 8,
    "apply_timestamp": "2025-05-30T11:33:06.528424"
  },
  "step_data": [
    {
      "step_no": "1",
      "action": "navigate",
      "locator_strategy": "",
      "locator": "",
      "test_data_param": "https://example.com/login",
      "expected_result": "Login page loads",
      "timeout": 10,
      "_is_ai_generated": true,
      "_is_locked": true
    },
    {
      "step_no": "2.1",
      "action": "wait",
      "locator_strategy": "",
      "locator": "",
      "test_data_param": "2",
      "expected_result": "Page stabilizes",
      "timeout": 5,
      "_is_manual": true,
      "_step_id": "manual_wait_001"
    }
  ]
}
```

## Implementation Details

### Core Components

#### 1. **StepDataStorage Class** (`core/step_data_storage.py`)
- **Purpose**: Handles JSON file operations with proper error handling
- **Key Methods**:
  - `save_step_data()`: Save step data with metadata
  - `load_step_data()`: Load latest step data for a test case
  - `_cleanup_old_files()`: Maintain version history limits

#### 2. **StateManager Integration** (`state_manager.py`)
- **Enhanced `get_effective_step_table()`**: Prioritizes JSON data over in-memory state
- **New Methods**:
  - `save_step_data_to_json()`: Wrapper for saving with test case context
  - `load_step_data_from_json()`: Wrapper for loading with error handling

#### 3. **Stage Integration**
- **Stage 3**: Saves initial AI-generated steps after conversion
- **Hybrid Editor**: Saves combined steps when "Apply to Test Case" is used
- **All Stages**: Load from JSON as primary data source

### Data Flow Priority

```
1. JSON File Data (Highest Priority)
   ↓ (if not found)
2. Combined Steps (Hybrid Editing)
   ↓ (if not available)
3. Original Step Table (Fallback)
```

### Automatic Save Triggers

1. **Initial Conversion** (Stage 3)
   - When test case is converted to automation format
   - Metadata: `source: "initial_conversion"`

2. **Hybrid Editing Sync** (StateManager)
   - When `sync_step_table_with_combined()` is called
   - Metadata: `source: "hybrid_editing_sync"`

3. **Apply to Test Case** (Hybrid Editor)
   - When user applies combined steps to test case
   - Metadata: `source: "hybrid_editing_apply"`

## Usage Examples

### Basic Usage
```python
from core.step_data_storage import get_step_data_storage

# Get storage instance
storage = get_step_data_storage()

# Save step data
success = storage.save_step_data(
    test_case_id="TC_001",
    step_data=step_list,
    metadata={"source": "manual_edit"}
)

# Load step data
result = storage.load_step_data("TC_001")
if result:
    step_data, metadata = result
```

### StateManager Integration
```python
# Save through StateManager
state.save_step_data_to_json(step_data, metadata)

# Load through StateManager
result = state.load_step_data_from_json()
```

## Configuration

### Storage Settings
```python
# In core/step_data_storage.py
STEP_DATA_DIR = "step_data_storage"
MAX_BACKUP_FILES = 10  # Keep last 10 versions per test case
```

### Metadata Fields
- **source**: Origin of the step data (e.g., "initial_conversion", "hybrid_editing_apply")
- **ai_steps_count**: Number of AI-generated steps
- **manual_steps_count**: Number of manually added steps
- **conversion_method**: How the steps were generated
- **validation_score**: AI completeness validation score
- **timestamps**: Various operation timestamps

## Benefits

### 🔧 **Reliability**
- **Data persistence**: No loss of hybrid edits on application restart
- **Version history**: Previous versions are preserved for recovery
- **Integrity checking**: Hash validation prevents data corruption

### 🚀 **Performance**
- **Efficient loading**: Only loads when needed
- **Minimal overhead**: JSON operations are fast and lightweight
- **Smart caching**: In-memory state is updated after JSON operations

### 🎯 **Consistency**
- **Single source of truth**: Eliminates data flow inconsistencies
- **Automatic synchronization**: All operations maintain JSON persistence
- **Cross-stage reliability**: All stages use the same authoritative data

## Troubleshooting

### Common Issues

1. **Permission Errors**
   - Ensure write permissions to the application directory
   - Check if antivirus is blocking file operations

2. **Corrupted JSON Files**
   - System automatically falls back to previous versions
   - Hash validation detects corruption

3. **Missing Step Data**
   - Check if test case ID is correct
   - Verify files exist in `step_data_storage/` directory

### Debugging

Enable detailed logging:
```python
import logging
logging.getLogger("core.step_data_storage").setLevel(logging.DEBUG)
```

### Recovery

If JSON files are corrupted:
1. Check `step_data_storage/` directory for backup versions
2. Manually restore from a previous version
3. Re-run the conversion process if needed

## Future Enhancements

- **Compression**: Compress older JSON files to save space
- **Cloud sync**: Optional cloud storage integration
- **Conflict resolution**: Handle concurrent edits from multiple sessions
- **Export/Import**: Bulk operations for step data management
